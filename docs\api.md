# 文件管理工具集 API 文档

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: JWT <PERSON>er <PERSON>ken
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 认证

### 用户注册
```http
POST /auth/register
```

**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "user",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 用户登录
```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "user",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 刷新令牌
```http
POST /auth/refresh
```

**请求体**:
```json
{
  "token": "string"
}
```

## 用户管理

### 获取用户资料
```http
GET /users/profile
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "user",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 更新用户资料
```http
PUT /users/profile
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "email": "<EMAIL>"
}
```

### 修改密码
```http
POST /users/change-password
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "old_password": "string",
  "new_password": "string"
}
```

## 文件管理

### 获取文件列表
```http
GET /files?directory_id=1&page=1&limit=20&search=keyword
Authorization: Bearer <token>
```

**查询参数**:
- `directory_id` (可选): 目录ID，0表示根目录
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认20
- `search` (可选): 搜索关键词

**响应**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": 1,
        "name": "file_123456.jpg",
        "original_name": "photo.jpg",
        "path": "./uploads/file_123456.jpg",
        "size": 1024000,
        "mime_type": "image/jpeg",
        "extension": "jpg",
        "directory_id": 1,
        "directory": {
          "id": 1,
          "name": "Photos",
          "path": "Photos"
        },
        "user_id": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 获取单个文件信息
```http
GET /files/{id}
Authorization: Bearer <token>
```

### 上传文件
```http
POST /files/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**表单数据**:
- `files`: 文件数组
- `directory_id` (可选): 目标目录ID

**响应**:
```json
{
  "success": true,
  "message": "Successfully uploaded 2 files",
  "files": [
    {
      "id": 1,
      "name": "file_123456.jpg",
      "original_name": "photo.jpg",
      "size": 1024000,
      "mime_type": "image/jpeg",
      "extension": "jpg"
    }
  ],
  "errors": []
}
```

### 下载文件
```http
GET /files/{id}/download
Authorization: Bearer <token>
```

**响应**: 文件二进制数据

### 删除文件
```http
DELETE /files/{id}
Authorization: Bearer <token>
```

### 更新文件信息
```http
PUT /files/{id}
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "name": "new_filename.jpg",
  "directory_id": 2
}
```

### 搜索文件
```http
POST /files/search
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "query": "photo",
  "extension": "jpg",
  "min_size": 1000,
  "max_size": 10000000,
  "start_date": "2024-01-01",
  "end_date": "2024-12-31"
}
```

## 目录管理

### 获取目录列表
```http
GET /directories?parent_id=1
Authorization: Bearer <token>
```

**查询参数**:
- `parent_id` (可选): 父目录ID，0表示根目录

### 创建目录
```http
POST /directories
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "name": "New Folder",
  "parent_id": 1
}
```

### 获取目录信息
```http
GET /directories/{id}
Authorization: Bearer <token>
```

### 更新目录
```http
PUT /directories/{id}
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "name": "Updated Folder Name"
}
```

### 删除目录
```http
DELETE /directories/{id}
Authorization: Bearer <token>
```

### 获取目录下的文件
```http
GET /directories/{id}/files
Authorization: Bearer <token>
```

## 批量重命名

### 预览重命名
```http
POST /rename/preview
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "file_ids": [1, 2, 3],
  "type": "regex",
  "pattern": "photo(\\d+)",
  "replacement": "image_$1"
}
```

**重命名类型**:
- `regex`: 正则表达式替换
- `sequence`: 序号重命名
- `prefix`: 添加前缀
- `suffix`: 添加后缀
- `case`: 大小写转换

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "original_name": "photo1.jpg",
      "new_name": "image_1.jpg",
      "valid": true
    },
    {
      "original_name": "photo2.jpg",
      "new_name": "image_2.jpg",
      "valid": true
    }
  ]
}
```

### 执行重命名
```http
POST /rename/execute
Authorization: Bearer <token>
```

**请求体**: 与预览重命名相同

**响应**:
```json
{
  "success": true,
  "message": "Successfully renamed 2 files",
  "data": {
    "renamed_files": [...],
    "success_count": 2,
    "total_count": 2
  }
}
```

### 获取重命名操作历史
```http
GET /rename/operations?page=1&limit=20
Authorization: Bearer <token>
```

### 保存重命名操作模板
```http
POST /rename/operations
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "type": "regex",
  "pattern": "photo(\\d+)",
  "replacement": "image_$1"
}
```

### 删除重命名操作模板
```http
DELETE /rename/operations/{id}
Authorization: Bearer <token>
```

## 操作日志

### 获取操作日志
```http
GET /logs?page=1&limit=20&action=upload&status=success&start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer <token>
```

**查询参数**:
- `page` (可选): 页码
- `limit` (可选): 每页数量
- `action` (可选): 操作类型
- `status` (可选): 状态
- `start_date` (可选): 开始日期
- `end_date` (可选): 结束日期

### 获取单个日志
```http
GET /logs/{id}
Authorization: Bearer <token>
```

### 删除日志
```http
DELETE /logs/{id}
Authorization: Bearer <token>
```

### 导出日志
```http
POST /logs/export
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "action": "upload",
  "status": "success",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "format": "csv"
}
```

**格式选项**:
- `csv`: CSV格式
- `json`: JSON格式

### 获取日志统计
```http
GET /logs/stats
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "action_stats": [
      {
        "action": "upload",
        "count": 150
      },
      {
        "action": "download",
        "count": 89
      }
    ],
    "status_stats": [
      {
        "status": "success",
        "count": 200
      },
      {
        "status": "failed",
        "count": 39
      }
    ],
    "daily_stats": [
      {
        "date": "2024-01-01",
        "count": 25
      }
    ]
  }
}
```

### 清理旧日志
```http
POST /logs/cleanup
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "days": 30
}
```

## 系统信息

### 获取系统信息
```http
GET /system/info
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "version": "1.0.0",
    "go_version": "1.21",
    "os": "linux",
    "arch": "amd64"
  }
}
```

### 获取统计信息
```http
GET /system/stats
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "file_count": 1250,
    "directory_count": 45,
    "total_size": **********
  }
}
```

## 健康检查

### 健康检查
```http
GET /health
```

**响应**:
```json
{
  "status": "ok",
  "message": "File Manager API is running"
}
```

## 错误响应

所有API在出错时都会返回统一的错误格式：

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information"
}
```

### 常见HTTP状态码

- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未授权或令牌无效
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突
- `422 Unprocessable Entity`: 请求参数验证失败
- `500 Internal Server Error`: 服务器内部错误

## 请求限制

- 文件上传大小限制: 100MB
- 请求频率限制: 100次/分钟
- 并发上传文件数: 10个

## 示例代码

### JavaScript/Axios
```javascript
// 设置基础配置
const api = axios.create({
  baseURL: 'http://localhost:8080/api/v1',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// 上传文件
const uploadFile = async (file, directoryId) => {
  const formData = new FormData();
  formData.append('files', file);
  if (directoryId) {
    formData.append('directory_id', directoryId);
  }
  
  const response = await api.post('/files/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  
  return response.data;
};

// 批量重命名预览
const previewRename = async (fileIds, renameConfig) => {
  const response = await api.post('/rename/preview', {
    file_ids: fileIds,
    ...renameConfig
  });
  
  return response.data;
};
```

### cURL
```bash
# 用户登录
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}'

# 上传文件
curl -X POST http://localhost:8080/api/v1/files/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files=@/path/to/file.jpg" \
  -F "directory_id=1"

# 获取文件列表
curl -X GET "http://localhost:8080/api/v1/files?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 版本历史

- **v1.0.0**: 初始版本
  - 基础文件管理功能
  - 批量重命名功能
  - 用户认证和权限管理
  - 操作日志记录

## 支持

如有问题或建议，请联系开发团队或提交Issue。