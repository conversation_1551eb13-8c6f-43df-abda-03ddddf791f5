/* 现代化极简设计系统 */
:root {
  /* 现代化色彩系统 - 简洁版 */
  --primary: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: #a5b4fc;
  --secondary: #64748b;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  
  /* 中性色彩 */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* 背景色彩 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-dark: #0f172a;
  --bg-card: #ffffff;
  --bg-hover: #f8fafc;
  
  /* 文字颜色 */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-muted: #64748b;
  --text-light: #94a3b8;
  --text-white: #ffffff;
  
  /* 边框颜色 */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: #6366f1;
  
  /* 阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* 圆角系统 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* 字体系统 */
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  
  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* 优化的过渡动画系统 */
  --transition-fast: 100ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-normal: 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-slow: 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* 侧边栏专用动画配置 - 优化为更快速响应 */
  --sidebar-transition: 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --sidebar-transition-timing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --sidebar-transition-duration: 200ms;
  
  /* 弹性动画 */
  --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
}

/* 现代化滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* 选择样式 */
::selection {
  background: var(--primary-light);
  color: var(--text-primary);
}

/* 链接样式 */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-hover);
}

/* 现代化按钮系统 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--text-sm);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  user-select: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 按钮变体 */
.btn-primary {
  background: var(--primary);
  color: var(--text-white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-card);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
}

.btn-xl {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

/* 现代化卡片系统 */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 统一所有页面标题大小 - 使用最高优先级选择器 */
.banner-title,
.welcome-title,
h1.banner-title,
h1.welcome-title,
.file-manager-banner .banner-title,
.file-manager-page .banner-title,
.dashboard-page .welcome-title,
.batch-rename-page .banner-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  line-height: 1.2 !important;
  margin: 0 !important;
}

/* 强制覆盖任何可能的Element Plus样式 */
.el-card .banner-title,
.el-container .banner-title,
div .banner-title,
section .banner-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  line-height: 1.2 !important;
}

/* 响应式标题大小 - 保持统一的1.25rem */
@media (max-width: 768px) {
  .banner-title,
  .welcome-title,
  h1.banner-title,
  h1.welcome-title,
  .file-manager-banner .banner-title,
  .file-manager-page .banner-title,
  .dashboard-page .welcome-title,
  .batch-rename-page .banner-title {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 480px) {
  .banner-title,
  .welcome-title,
  h1.banner-title,
  h1.welcome-title,
  .file-manager-banner .banner-title,
  .file-manager-page .banner-title,
  .dashboard-page .welcome-title,
  .batch-rename-page .banner-title {
    font-size: 1.25rem !important;
  }
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: var(--space-1) 0 0 0;
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-tertiary);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

/* 现代化输入框系统 */
.input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-family: inherit;
  transition: all var(--transition-fast);
}

.input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.input::placeholder {
  color: var(--text-muted);
}

.input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-tertiary);
}

/* 输入组 */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--space-3);
  color: var(--text-muted);
  font-size: var(--text-lg);
  pointer-events: none;
  z-index: 10;
}

.input-with-icon {
  padding-left: var(--space-10);
}

/* 现代化表格系统 */
.table-container {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--space-4) var(--space-6);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.table th {
  background: var(--bg-tertiary);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.table td {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.table tr:hover {
  background: var(--bg-hover);
}

.table tr:last-child td {
  border-bottom: none;
}

/* 现代化布局系统 */
.layout-container {
  display: flex;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.layout-sidebar {
  width: 280px;
  background: var(--bg-card);
  border-right: 1px solid var(--border-primary);
  transition: all var(--sidebar-transition);
  position: relative;
  z-index: 100;
}

.layout-sidebar.collapsed {
  width: 80px;
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.layout-header {
  height: 80px;
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-8);
  position: sticky;
  top: 0;
  z-index: 50;
}

.layout-content {
  flex: 1;
  padding: var(--space-8);
  overflow-y: auto;
}

/* 现代化导航系统 */
.nav {
  padding: var(--space-6);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  margin-bottom: var(--space-1);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--primary);
  color: var(--text-white);
}

.nav-icon {
  font-size: var(--text-lg);
  width: 24px;
  text-align: center;
}

.nav-text {
  font-weight: 500;
  transition: opacity var(--transition-normal);
}

.layout-sidebar.collapsed .nav-text {
  opacity: 0;
}

/* 现代化统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stats-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary);
}

.stats-card.success::before {
  background: var(--success);
}

.stats-card.warning::before {
  background: var(--warning);
}

.stats-card.danger::before {
  background: var(--danger);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stats-value {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.stats-label {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
}

.stats-icon {
  position: absolute;
  top: var(--space-6);
  right: var(--space-6);
  font-size: var(--text-2xl);
  color: var(--text-light);
}

/* 现代化文件网格 */
.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: var(--space-6);
  padding: var(--space-6);
}

.file-item {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
}

.file-item:hover {
  transform: translateY(-2px);
  border-color: var(--primary);
  box-shadow: var(--shadow-lg);
}

.file-item.selected {
  border-color: var(--primary);
  background: rgb(99 102 241 / 0.05);
}

.file-icon {
  font-size: 3rem;
  color: var(--primary);
  text-align: center;
  margin-bottom: var(--space-4);
  display: block;
}

.file-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  word-break: break-word;
  line-height: 1.4;
}

.file-info {
  font-size: var(--text-xs);
  color: var(--text-muted);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 现代化加载状态 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-200);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
  gap: var(--space-4);
}

/* 现代化空状态 */
.empty-state {
  text-align: center;
  padding: var(--space-16);
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--space-6);
  color: var(--text-light);
}

.empty-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.empty-description {
  font-size: var(--text-sm);
  color: var(--text-muted);
  max-width: 400px;
  margin: 0 auto var(--space-6);
  line-height: 1.6;
}

/* 现代化面包屑 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
  font-size: var(--text-sm);
}

.breadcrumb-item {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
}

.breadcrumb-item:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
}

.breadcrumb-item.active {
  color: var(--text-primary);
  font-weight: 500;
}

.breadcrumb-separator {
  color: var(--text-light);
  font-size: var(--text-xs);
}

/* 现代化搜索框 */
.search-container {
  position: relative;
  max-width: 400px;
}

.search-input {
  padding-left: var(--space-10);
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--text-sm);
  pointer-events: none;
}

/* 现代化拖拽区域 */
.drop-zone {
  border: 2px dashed var(--border-secondary);
  border-radius: var(--radius-xl);
  padding: var(--space-16);
  text-align: center;
  color: var(--text-secondary);
  transition: all var(--transition-normal);
  background: var(--bg-card);
}

.drop-zone:hover,
.drop-zone.drag-over {
  border-color: var(--primary);
  color: var(--text-primary);
  background: rgb(99 102 241 / 0.05);
}

.drop-zone-icon {
  font-size: 3rem;
  margin-bottom: var(--space-6);
  color: var(--text-light);
}

.drop-zone-text {
  font-size: var(--text-lg);
  font-weight: 500;
  margin-bottom: var(--space-2);
}

.drop-zone-hint {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

/* 工具提示 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gray-900);
  color: var(--text-white);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-fast);
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .layout-sidebar {
    width: 250px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: -280px;
    z-index: 1000;
    height: 100vh;
    transition: left var(--transition-normal);
  }
  
  .layout-sidebar.show {
    left: 0;
  }
  
  .layout-main {
    margin-left: 0;
  }
  
  .layout-content {
    padding: var(--space-6);
  }
  
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--space-4);
    padding: var(--space-4);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .card-header,
  .card-body,
  .card-footer {
    padding: var(--space-4);
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 特殊页面样式 - 现代化登录界面 */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #6366f1 50%,
    #8b5cf6 75%,
    #a855f7 100%);
  padding: var(--space-8);
  position: relative;
  overflow: hidden;
}

/* 动态背景装饰 */
.auth-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

.auth-container::after {
  content: '';
  position: absolute;
  top: 20%;
  right: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

.auth-card {
  width: 100%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  padding: var(--space-10);
  position: relative;
  z-index: 10;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.auth-logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary) 0%, #8b5cf6 100%);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
  color: var(--text-white);
  font-size: var(--text-3xl);
  font-weight: 700;
  box-shadow:
    0 10px 25px -5px rgba(99, 102, 241, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.auth-logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.auth-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.auth-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
}

.auth-form {
  margin-bottom: var(--space-6);
}

.auth-form .input {
  margin-bottom: var(--space-4);
}

.auth-form .btn {
  width: 100%;
  margin-bottom: var(--space-4);
}

.auth-footer {
  text-align: center;
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.auth-link {
  color: var(--primary);
  font-weight: 500;
  margin-left: var(--space-1);
}

.auth-link:hover {
  color: var(--primary-hover);
}
.auth-title {
  font-size: var(--text-3xl);
  font-weight: 800;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 var(--space-3) 0;
  line-height: 1.2;
}

.auth-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0;
  font-weight: 500;
}

.auth-form {
  margin-bottom: var(--space-8);
}

.auth-form .input {
  margin-bottom: var(--space-5);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.auth-form .input:focus {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--primary);
  box-shadow: 
    0 0 0 3px rgba(99, 102, 241, 0.1),
    0 4px 12px rgba(99, 102, 241, 0.15);
  transform: translateY(-1px);
}

.auth-form .input::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

.auth-form .btn {
  width: 100%;
  margin-bottom: var(--space-6);
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
  font-weight: 600;
  background: linear-gradient(135deg, var(--primary) 0%, #8b5cf6 100%);
  border: none;
  box-shadow: 
    0 10px 25px -5px rgba(99, 102, 241, 0.4),
    0 4px 6px -2px rgba(99, 102, 241, 0.1);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.auth-form .btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 
    0 15px 35px -5px rgba(99, 102, 241, 0.5),
    0 8px 15px -5px rgba(99, 102, 241, 0.2);
}

.auth-form .btn:active {
  transform: translateY(0);
}

.auth-form .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.auth-form .btn:hover::before {
  left: 100%;
}

.auth-footer {
  text-align: center;
  padding-top: var(--space-6);
  border-top: 1px solid rgba(226, 232, 240, 0.3);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.auth-link {
  color: var(--primary);
  font-weight: 600;
  margin-left: var(--space-1);
  text-decoration: none;
  position: relative;
  transition: all var(--transition-fast);
}

.auth-link:hover {
  color: var(--primary-hover);
  transform: translateY(-1px);
}

.auth-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--primary) 0%, #8b5cf6 100%);
  transition: width var(--transition-normal);
}

.auth-link:hover::after {
  width: 100%;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .auth-container {
    padding: var(--space-4);
  }
  
  .auth-card {
    padding: var(--space-8);
    max-width: 100%;
  }
  
  .auth-logo {
    width: 70px;
    height: 70px;
    font-size: var(--text-2xl);
  }
  
  .auth-title {
    font-size: var(--text-2xl);
  }
}