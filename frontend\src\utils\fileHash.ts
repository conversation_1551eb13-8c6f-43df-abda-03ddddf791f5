/**
 * 文件哈希计算工具
 * 用于在前端计算文件的SHA-256哈希值，以便进行重复检测
 */

/**
 * 计算文件的SHA-256哈希值
 * @param file 要计算哈希的文件
 * @returns Promise<string> 文件的SHA-256哈希值（十六进制字符串）
 */
export async function calculateFileHash(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = async (event) => {
      try {
        const arrayBuffer = event.target?.result as ArrayBuffer
        const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer)
        const hashArray = Array.from(new Uint8Array(hashBuffer))
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
        resolve(hashHex)
      } catch (error) {
        reject(error)
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
    
    reader.readAsArrayBuffer(file)
  })
}

/**
 * 批量计算文件哈希值
 * @param files 文件列表
 * @param onProgress 进度回调函数
 * @returns Promise<Map<string, string>> 文件名到哈希值的映射
 */
export async function calculateFilesHash(
  files: File[], 
  onProgress?: (completed: number, total: number) => void
): Promise<Map<string, string>> {
  const hashMap = new Map<string, string>()
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    try {
      const hash = await calculateFileHash(file)
      hashMap.set(file.name, hash)
      
      if (onProgress) {
        onProgress(i + 1, files.length)
      }
    } catch (error) {
      console.warn(`Failed to calculate hash for file ${file.name}:`, error)
      // 如果计算失败，可以跳过或使用文件名+大小作为标识
    }
  }
  
  return hashMap
}

/**
 * 检查文件是否为小文件（用于决定是否计算哈希）
 * @param file 文件对象
 * @param maxSize 最大文件大小（字节），默认10MB
 * @returns boolean 是否为小文件
 */
export function isSmallFile(file: File, maxSize: number = 10 * 1024 * 1024): boolean {
  return file.size <= maxSize
}

/**
 * 生成文件的简单标识符（文件名+大小+修改时间）
 * 用于大文件或无法计算哈希时的快速重复检测
 * @param file 文件对象
 * @returns string 文件标识符
 */
export function generateFileIdentifier(file: File): string {
  return `${file.name}_${file.size}_${file.lastModified}`
}

/**
 * 智能文件重复检测
 * 对小文件计算哈希，对大文件使用标识符
 * @param files 文件列表
 * @param onProgress 进度回调
 * @returns Promise<Array<{file: File, hash?: string, identifier: string}>>
 */
export async function smartDuplicateDetection(
  files: File[],
  onProgress?: (completed: number, total: number, currentFile: string) => void
): Promise<Array<{file: File, hash?: string, identifier: string}>> {
  const results: Array<{file: File, hash?: string, identifier: string}> = []
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const identifier = generateFileIdentifier(file)
    
    if (onProgress) {
      onProgress(i, files.length, file.name)
    }
    
    let hash: string | undefined
    
    // 对小文件计算哈希值
    if (isSmallFile(file)) {
      try {
        hash = await calculateFileHash(file)
      } catch (error) {
        console.warn(`Failed to calculate hash for ${file.name}:`, error)
      }
    }
    
    results.push({
      file,
      hash,
      identifier
    })
  }
  
  if (onProgress) {
    onProgress(files.length, files.length, '')
  }
  
  return results
}
