<template>
  <div class="auth-container">
    <div class="auth-card">
      <!-- 头部区域 -->
      <div class="auth-header">
        <div class="auth-logo">
          <i class="el-icon-user-solid"></i>
        </div>
        <h1 class="auth-title">创建新账户</h1>
        <p class="auth-subtitle">加入文件管理工具集</p>
      </div>
      
      <!-- 表单区域 -->
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="auth-form"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="用户名"
            size="large"
            prefix-icon="el-icon-user"
          />
        </el-form-item>
        
        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="邮箱地址"
            size="large"
            prefix-icon="el-icon-message"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="密码"
            size="large"
            prefix-icon="el-icon-lock"
            show-password
          />
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="确认密码"
            size="large"
            prefix-icon="el-icon-lock"
            show-password
            @keyup.enter="handleRegister"
          />
        </el-form-item>
        
        <el-form-item>
          <button
            type="button"
            :disabled="loading"
            class="btn btn-primary btn-xl"
            @click="handleRegister"
          >
            <span v-if="loading" class="loading"></span>
            <span v-else>注册</span>
          </button>
        </el-form-item>
      </el-form>
      
      <!-- 底部链接 -->
      <div class="auth-footer">
        <span>已有账户？</span>
        <router-link to="/login" class="auth-link">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import type { RegisterRequest } from '@/types'

const router = useRouter()
const authStore = useAuthStore()

const registerFormRef = ref<FormInstance>()
const loading = ref(false)

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const registerRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: ['blur', 'change'] },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: ['blur', 'change'] }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: ['blur', 'change'] },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: ['blur', 'change'] },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: ['blur', 'change'] }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: ['blur', 'change'] }
  ]
}

const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate()
    loading.value = true
    
    const registerData: RegisterRequest = {
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password
    }
    
    const result = await authStore.register(registerData)
    
    if (result.success) {
      ElMessage.success('注册成功')
      router.push('/dashboard')
    } else {
      ElMessage.error(result.message || '注册失败')
    }
  } catch (error) {
    console.error('注册失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 清除初始验证状态
  nextTick(() => {
    if (registerFormRef.value) {
      registerFormRef.value.clearValidate()
    }
  })
})
</script>

<style scoped>
/* Element Plus 表单样式重置和优化 */
.auth-form :deep(.el-form-item) {
  margin-bottom: var(--space-4);
  position: relative;
}

.auth-form :deep(.el-input) {
  height: auto;
}

.auth-form :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-4);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
  box-shadow: none;
  min-height: 48px;
}

.auth-form :deep(.el-input__wrapper:hover) {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.5);
}

.auth-form :deep(.el-input.is-focus .el-input__wrapper) {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--primary);
  box-shadow:
    0 0 0 3px rgba(99, 102, 241, 0.1),
    0 4px 12px rgba(99, 102, 241, 0.15);
  transform: translateY(-1px);
}

.auth-form :deep(.el-input__inner) {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: var(--text-sm);
  padding: 0;
  height: auto;
  line-height: 1.5;
}

.auth-form :deep(.el-input__inner::placeholder) {
  color: var(--text-muted);
  font-weight: 400;
}

.auth-form :deep(.el-input__prefix) {
  color: var(--text-muted);
}

.auth-form :deep(.el-input__suffix) {
  color: var(--text-muted);
}

.auth-form :deep(.el-input__suffix-inner) {
  display: flex;
  align-items: center;
}

/* 表单验证错误样式 */
.auth-form :deep(.el-form-item.is-error .el-input__wrapper) {
  border-color: var(--danger);
  background: rgba(239, 68, 68, 0.08);
  box-shadow:
    0 0 0 3px rgba(239, 68, 68, 0.1),
    0 4px 12px rgba(239, 68, 68, 0.15);
  animation: shake 0.3s ease-in-out;
}

/* 隐藏错误信息文本，只保留视觉指示 */
.auth-form :deep(.el-form-item__error) {
  display: none;
}

/* 错误状态动画效果 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* 错误状态的输入框占位符颜色调整 */
.auth-form :deep(.el-form-item.is-error .el-input__inner::placeholder) {
  color: rgba(239, 68, 68, 0.6);
}

/* 按钮样式重置 */
.auth-form .btn {
  width: 100%;
  margin-bottom: var(--space-6);
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
  font-weight: 600;
  background: linear-gradient(135deg, var(--primary) 0%, #8b5cf6 100%);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  cursor: pointer;
  box-shadow:
    0 10px 25px -5px rgba(99, 102, 241, 0.4),
    0 4px 6px -2px rgba(99, 102, 241, 0.1);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.auth-form .btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow:
    0 15px 35px -5px rgba(99, 102, 241, 0.5),
    0 8px 15px -5px rgba(99, 102, 241, 0.2);
}

.auth-form .btn:active {
  transform: translateY(0);
}

.auth-form .btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.auth-form .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.auth-form .btn:hover::before {
  left: 100%;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-container {
    padding: var(--space-4);
  }
  
  .auth-card {
    padding: var(--space-6);
  }
  
  .auth-title {
    font-size: var(--text-xl);
  }
  
  .auth-logo {
    width: 50px;
    height: 50px;
    font-size: var(--text-xl);
  }
}
</style>