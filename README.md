# 文件管理工具集 (File Management Toolkit)

一个基于Go后端和Vue.js前端的现代化文件管理Web应用程序。

## 核心功能

### 批量文件重命名模块
- 正则表达式替换
- 序号添加
- 前后缀修改
- 大小写转换
- 实时预览功能

### 文件管理模块
- 文件上传下载
- 目录浏览
- 文件搜索过滤
- 权限管理
- 文件信息展示
- 拖拽操作
- 批量选择

### 系统特性
- RESTful API设计
- 响应式用户界面
- 错误处理机制
- 操作日志记录
- 撤销功能
- 跨平台兼容性

## 技术架构

### 后端 (Go)
- Gin Web框架
- GORM ORM
- JWT认证
- SQLite数据库
- 结构化日志

### 前端 (Vue.js)
- Vue 3 + TypeScript
- Element Plus UI
- Pinia状态管理
- 响应式设计

## 快速开始

### 后端启动
```bash
cd backend
go mod tidy
go run main.go
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

## API文档

详见 `docs/api.md`

## 部署指南

详见 `docs/deployment.md`