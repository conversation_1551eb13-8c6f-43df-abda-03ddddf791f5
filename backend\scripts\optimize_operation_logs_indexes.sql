-- 操作日志表索引优化脚本
-- 用于提高筛选查询的性能

-- 1. 复合索引：用户ID + 筛选条件 + 创建时间
-- 这个索引可以显著提高带有筛选条件的查询性能
CREATE INDEX IF NOT EXISTS idx_operation_logs_user_filters 
ON operation_logs(user_id, action, status, created_at DESC);

-- 2. 用户ID + 创建时间索引（用于时间范围查询）
CREATE INDEX IF NOT EXISTS idx_operation_logs_user_time 
ON operation_logs(user_id, created_at DESC);

-- 3. 用户ID + 操作类型索引（用于操作类型筛选）
CREATE INDEX IF NOT EXISTS idx_operation_logs_user_action 
ON operation_logs(user_id, action);

-- 4. 用户ID + 状态索引（用于状态筛选）
CREATE INDEX IF NOT EXISTS idx_operation_logs_user_status 
ON operation_logs(user_id, status);

-- 5. 统计查询优化索引
CREATE INDEX IF NOT EXISTS idx_operation_logs_stats 
ON operation_logs(user_id, action, status);

-- 6. 日期统计索引（用于日统计查询）
CREATE INDEX IF NOT EXISTS idx_operation_logs_daily_stats 
ON operation_logs(user_id, DATE(created_at));

-- 查看索引创建结果
SELECT name, sql FROM sqlite_master 
WHERE type='index' AND tbl_name='operation_logs' 
ORDER BY name;

-- 分析表统计信息（SQLite）
ANALYZE operation_logs;

-- 使用说明：
-- 1. 在生产环境中运行此脚本前，请先备份数据库
-- 2. 建议在低峰期执行索引创建操作
-- 3. 创建索引后，可以使用 EXPLAIN QUERY PLAN 分析查询性能
-- 4. 定期运行 ANALYZE 命令更新统计信息

-- 示例查询性能分析：
-- EXPLAIN QUERY PLAN 
-- SELECT * FROM operation_logs 
-- WHERE user_id = 1 AND action = 'upload' AND status = 'success' 
-- ORDER BY created_at DESC LIMIT 20;