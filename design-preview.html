<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理工具集 - 设计预览</title>
    <style>
        /* 导入新的设计系统样式 */
        :root {
            /* 颜色系统 */
            --primary: rgb(99 102 241);
            --primary-hover: rgb(79 70 229);
            --primary-light: rgb(99 102 241 / 0.1);
            --success: rgb(34 197 94);
            --warning: rgb(245 158 11);
            --danger: rgb(239 68 68);
            --info: rgb(59 130 246);
            
            /* 文本颜色 */
            --text-primary: rgb(15 23 42);
            --text-secondary: rgb(71 85 105);
            --text-tertiary: rgb(148 163 184);
            --text-inverse: rgb(248 250 252);
            
            /* 背景颜色 */
            --bg-primary: rgb(248 250 252);
            --bg-secondary: rgb(241 245 249);
            --bg-tertiary: rgb(226 232 240);
            --bg-card: rgb(255 255 255);
            --bg-hover: rgb(248 250 252);
            
            /* 边框颜色 */
            --border-primary: rgb(226 232 240);
            --border-secondary: rgb(203 213 225);
            --border-focus: var(--primary);
            
            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            
            /* 间距 */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            
            /* 圆角 */
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --radius-2xl: 1rem;
            --radius-full: 9999px;
            
            /* 字体大小 */
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
            
            /* 过渡 */
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 200ms ease-in-out;
            --transition-slow: 300ms ease-in-out;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-8);
        }

        .header {
            text-align: center;
            margin-bottom: var(--space-12);
        }

        .header h1 {
            font-size: var(--text-4xl);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-4);
        }

        .header p {
            font-size: var(--text-lg);
            color: var(--text-secondary);
        }

        .section {
            margin-bottom: var(--space-12);
        }

        .section-title {
            font-size: var(--text-2xl);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-3);
            border-bottom: 2px solid var(--border-primary);
        }

        /* 登录卡片样式 */
        .auth-container {
            display: flex;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            padding: var(--space-6);
        }

        .auth-card {
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            padding: var(--space-10);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .auth-logo {
            width: 80px;
            height: 80px;
            background: var(--primary);
            border-radius: var(--radius-2xl);
            margin: 0 auto var(--space-6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--text-2xl);
            font-weight: bold;
        }

        .auth-title {
            font-size: var(--text-3xl);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .auth-subtitle {
            color: var(--text-secondary);
            margin-bottom: var(--space-8);
        }

        /* 统计卡片样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .stats-card {
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            gap: var(--space-4);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary);
        }

        .stats-card.success::before {
            background: var(--success);
        }

        .stats-card.warning::before {
            background: var(--warning);
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-light);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: var(--text-2xl);
        }

        .stats-content {
            flex: 1;
        }

        .stats-value {
            font-size: var(--text-3xl);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-1);
            line-height: 1.2;
        }

        .stats-label {
            color: var(--text-secondary);
            font-size: var(--text-sm);
            font-weight: 500;
        }

        /* 快速操作卡片 */
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--space-6);
        }

        .card {
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal);
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .action-card {
            padding: var(--space-6);
            text-align: center;
            cursor: pointer;
        }

        .action-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-light);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: var(--text-2xl);
            margin: 0 auto var(--space-4);
        }

        .action-title {
            font-size: var(--text-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--space-2) 0;
        }

        .action-description {
            font-size: var(--text-sm);
            color: var(--text-secondary);
            margin: 0;
            line-height: 1.5;
        }

        .demo-section {
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            margin-bottom: var(--space-8);
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-8);
            margin-top: var(--space-6);
        }

        .comparison-item h4 {
            color: var(--text-primary);
            margin-bottom: var(--space-4);
            padding: var(--space-3);
            border-radius: var(--radius-lg);
        }

        .old-design h4 {
            background: rgb(239 68 68 / 0.1);
            color: var(--danger);
        }

        .new-design h4 {
            background: rgb(34 197 94 / 0.1);
            color: var(--success);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: var(--space-2) 0;
            color: var(--text-secondary);
            position: relative;
            padding-left: var(--space-6);
        }

        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--success);
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .action-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>文件管理工具集</h1>
            <p>现代化界面设计升级预览</p>
        </div>

        <div class="section">
            <h2 class="section-title">设计系统概览</h2>
            <div class="demo-section">
                <h3>设计改进对比</h3>
                <div class="comparison">
                    <div class="comparison-item old-design">
                        <h4>旧设计问题</h4>
                        <ul class="feature-list">
                            <li>过于花哨的玻璃态效果</li>
                            <li>不一致的设计语言</li>
                            <li>过多装饰性元素</li>
                            <li>登录页与仪表板风格不统一</li>
                            <li>缺乏现代感</li>
                        </ul>
                    </div>
                    <div class="comparison-item new-design">
                        <h4>新设计优势</h4>
                        <ul class="feature-list">
                            <li>简洁现代的设计语言</li>
                            <li>统一的视觉风格</li>
                            <li>专业的色彩系统</li>
                            <li>优雅的交互效果</li>
                            <li>响应式设计</li>
                            <li>无障碍访问支持</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">登录界面预览</h2>
            <div class="auth-container" style="min-height: 400px;">
                <div class="auth-card">
                    <div class="auth-logo">文</div>
                    <h2 class="auth-title">文件管理工具集</h2>
                    <p class="auth-subtitle">请登录您的账户</p>
                    <div style="text-align: left; color: var(--text-secondary); font-size: var(--text-sm);">
                        ✓ 简洁的卡片式设计<br>
                        ✓ 统一的品牌色彩<br>
                        ✓ 优雅的阴影效果
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">仪表板组件预览</h2>
            
            <h3 style="margin-bottom: var(--space-4); color: var(--text-primary);">统计卡片</h3>
            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-icon">📁</div>
                    <div class="stats-content">
                        <div class="stats-value">1,234</div>
                        <div class="stats-label">文件总数</div>
                    </div>
                </div>
                <div class="stats-card success">
                    <div class="stats-icon">💾</div>
                    <div class="stats-content">
                        <div class="stats-value">2.5GB</div>
                        <div class="stats-label">存储空间</div>
                    </div>
                </div>
                <div class="stats-card warning">
                    <div class="stats-icon">👤</div>
                    <div class="stats-content">
                        <div class="stats-value">admin</div>
                        <div class="stats-label">当前用户</div>
                    </div>
                </div>
            </div>

            <h3 style="margin: var(--space-8) 0 var(--space-4); color: var(--text-primary);">快速操作</h3>
            <div class="action-grid">
                <div class="action-card card">
                    <div class="action-icon">📤</div>
                    <h3 class="action-title">文件管理</h3>
                    <p class="action-description">上传、下载和管理您的文件</p>
                </div>
                <div class="action-card card">
                    <div class="action-icon">✏️</div>
                    <h3 class="action-title">批量重命名</h3>
                    <p class="action-description">使用多种规则批量重命名文件</p>
                </div>
                <div class="action-card card">
                    <div class="action-icon">📋</div>
                    <h3 class="action-title">操作日志</h3>
                    <p class="action-description">查看所有操作的详细记录</p>
                </div>
                <div class="action-card card">
                    <div class="action-icon">⚙️</div>
                    <h3 class="action-title">个人设置</h3>
                    <p class="action-description">管理您的账户和偏好设置</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">设计特点</h2>
            <div class="demo-section">
                <div class="stats-grid">
                    <div class="card" style="padding: var(--space-6);">
                        <h4 style="color: var(--primary); margin-bottom: var(--space-3);">🎨 现代设计</h4>
                        <p style="color: var(--text-secondary); font-size: var(--text-sm); margin: 0;">采用现代扁平化设计，去除冗余装饰，专注于内容和功能</p>
                    </div>
                    <div class="card" style="padding: var(--space-6);">
                        <h4 style="color: var(--success); margin-bottom: var(--space-3);">🔄 统一性</h4>
                        <p style="color: var(--text-secondary); font-size: var(--text-sm); margin: 0;">所有页面使用统一的设计语言，确保用户体验的一致性</p>
                    </div>
                    <div class="card" style="padding: var(--space-6);">
                        <h4 style="color: var(--warning); margin-bottom: var(--space-3);">📱 响应式</h4>
                        <p style="color: var(--text-secondary); font-size: var(--text-sm); margin: 0;">完美适配各种设备尺寸，提供优秀的移动端体验</p>
                    </div>
                    <div class="card" style="padding: var(--space-6);">
                        <h4 style="color: var(--info); margin-bottom: var(--space-3);">♿ 无障碍</h4>
                        <p style="color: var(--text-secondary); font-size: var(--text-sm); margin: 0;">遵循无障碍设计原则，确保所有用户都能轻松使用</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>