import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Layout from '@/components/Layout.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/Register.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      component: Layout,
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue')
        },
        {
          path: 'files',
          name: 'FileManager',
          component: () => import('@/views/FileManager.vue')
        },
        {
          path: 'file-manager',
          redirect: '/files'
        },
        {
          path: 'rename',
          name: 'BatchRename',
          component: () => import('@/views/BatchRename.vue')
        },
        {
          path: 'batch-rename',
          redirect: '/rename'
        },
        {
          path: 'logs',
          name: 'OperationLogs',
          component: () => import('@/views/OperationLogs.vue')
        },
        {
          path: 'operation-logs',
          redirect: '/logs'
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/views/Profile.vue')
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue')
    }
  ]
})

// 路由守卫 - 恢复认证检查
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 初始化认证状态
  if (!authStore.isAuthenticated && localStorage.getItem('token')) {
    authStore.initAuth()
  }
  
  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)
  
  if (requiresAuth && !authStore.isAuthenticated) {
    // 需要认证但未登录，重定向到登录页
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  } else if ((to.path === '/login' || to.path === '/register') && authStore.isAuthenticated) {
    // 已登录用户访问登录/注册页，重定向到首页
    next({ path: '/dashboard' })
  } else {
    // 允许访问
    next()
  }
})

export default router