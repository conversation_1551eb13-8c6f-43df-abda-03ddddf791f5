# 产品上下文 - 文件管理工具集

## 项目概述
**项目名称**: 文件管理工具集 (go-plan-rename)
**项目类型**: 全栈Web应用
**主要功能**: 批量文件重命名 + 文件管理

## 核心功能模块

### 1. 批量文件重命名模块
- **正则表达式重命名**: 支持复杂的模式匹配和替换
- **序号重命名**: 按序列号批量重命名文件
- **前缀/后缀添加**: 批量添加前缀或后缀
- **大小写转换**: 支持多种大小写转换模式
- **预览功能**: 重命名前可预览结果
- **撤销功能**: 支持操作撤销

### 2. 文件管理模块
- **文件上传**: 支持多文件上传，带进度显示
- **文件下载**: 单文件和批量下载
- **文件删除**: 支持批量删除操作
- **文件搜索**: 按文件名、类型、大小等条件搜索
- **目录管理**: 创建、删除、重命名目录
- **文件预览**: 支持常见文件类型预览

### 3. 用户管理模块
- **用户注册/登录**: JWT身份认证
- **权限控制**: 基于角色的访问控制
- **个人资料管理**: 用户信息修改
- **操作日志**: 详细的用户操作记录

## 技术架构

### 后端技术栈
- **语言**: Go 1.21+
- **Web框架**: Gin
- **数据库**: SQLite (GORM ORM)
- **认证**: JWT Token
- **文件存储**: 本地文件系统
- **日志**: 结构化日志记录

### 前端技术栈
- **框架**: Vue.js 3 + TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 项目结构
```
go-plan-rename/
├── backend/           # Go后端服务
│   ├── main.go       # 应用入口
│   ├── config/       # 配置管理
│   ├── controllers/  # 控制器层
│   ├── models/       # 数据模型
│   ├── middleware/   # 中间件
│   ├── routes/       # 路由定义
│   ├── utils/        # 工具函数
│   └── uploads/      # 文件上传目录
├── frontend/         # Vue.js前端应用
│   ├── src/
│   │   ├── views/    # 页面组件
│   │   ├── stores/   # Pinia状态管理
│   │   ├── utils/    # 工具函数
│   │   ├── types/    # TypeScript类型定义
│   │   ├── components/ # 布局组件
│   │   │   ├── Layout.vue    # 主布局容器
│   │   │   ├── Sidebar.vue   # 可折叠侧边栏
│   │   │   └── Header.vue    # 顶部栏组件
│   │   └── router/   # 路由配置
│   └── public/       # 静态资源
├── docs/             # 项目文档
└── memory-bank/      # 项目记忆库
```

## 部署信息
- **后端端口**: 8080
- **前端端口**: 5173 (开发环境)
- **数据库**: SQLite文件 `file_manager.db`
- **上传目录**: `backend/uploads/`

## 关键特性
1. **类型安全**: 全面的TypeScript类型定义
2. **响应式设计**: 适配各种屏幕尺寸
3. **国际化**: 完整的中文界面
4. **错误处理**: 完善的错误处理和用户反馈
5. **安全性**: JWT认证 + 密码加密存储
7. **现代化UI设计**: 玻璃拟态设计风格，渐变背景，现代化交互体验
8. **可折叠侧边栏**: 专业级导航系统，支持展开/折叠，功能分组，状态持久化
6. **可扩展性**: 模块化架构，易于扩展新功能