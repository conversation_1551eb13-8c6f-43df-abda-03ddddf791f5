{"name": "file-manager-frontend", "version": "1.0.0", "description": "文件管理工具集前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.5.0", "dayjs": "^1.11.9", "element-plus": "^2.3.9", "file-saver": "^2.0.5", "pinia": "^2.1.6", "sortablejs": "^1.15.0", "vue": "^3.3.4", "vue-draggable-plus": "^0.2.5", "vue-router": "^4.2.4"}, "devDependencies": {"@types/file-saver": "^2.0.5", "@types/node": "^20.5.9", "@types/sortablejs": "^1.15.2", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.4.1", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.2", "typescript": "~5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^7.0.6", "vue-tsc": "^1.8.8"}}