package database

import (
	"file-manager/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	_ "modernc.org/sqlite"
)

func Initialize(databaseURL string) (*gorm.DB, error) {
	// 添加SQLite连接参数以改善并发性能
	dsn := databaseURL + "?_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000&_timeout=5000&_busy_timeout=5000"

	db, err := gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        dsn,
	}, &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 设置最大打开连接数为1（SQLite推荐）
	sqlDB.SetMaxOpenConns(1)
	// 设置最大空闲连接数
	sqlDB.SetMaxIdleConns(1)

	// 自动迁移数据库表
	err = db.AutoMigrate(
		&models.User{},
		&models.File{},
		&models.Directory{},
		&models.RenameOperation{},
		&models.OperationLog{},
	)
	if err != nil {
		return nil, err
	}

	return db, nil
}
