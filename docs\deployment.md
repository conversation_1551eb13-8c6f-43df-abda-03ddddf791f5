# 文件管理工具集部署指南

## 系统要求

### 后端要求
- Go 1.21 或更高版本
- SQLite 3.x（默认）或 PostgreSQL 12+
- 至少 512MB RAM
- 至少 1GB 磁盘空间

### 前端要求
- Node.js 18+ 
- npm 8+ 或 yarn 1.22+

## 开发环境部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd file-manager
```

### 2. 后端部署

#### 安装依赖
```bash
cd backend
go mod tidy
```

#### 配置环境变量
复制 `.env` 文件并根据需要修改：
```bash
cp .env.example .env
```

主要配置项：
```env
# 环境配置
ENVIRONMENT=development
PORT=8080

# 数据库配置
DATABASE_URL=file_manager.db

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=104857600
ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,txt,doc,docx,xls,xlsx,zip,rar,mp4,mp3,avi,mov

# 日志配置
LOG_LEVEL=info
```

#### 启动后端服务
```bash
go run main.go
```

后端服务将在 `http://localhost:8080` 启动

### 3. 前端部署

#### 安装依赖
```bash
cd frontend
npm install
```

#### 启动开发服务器
```bash
npm run dev
```

前端服务将在 `http://localhost:5173` 启动

## 生产环境部署

### 1. 后端生产部署

#### 构建二进制文件
```bash
cd backend
CGO_ENABLED=1 GOOS=linux go build -a -ldflags '-extldflags "-static"' -o file-manager main.go
```

#### 使用 Docker 部署
创建 `Dockerfile`：
```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY backend/ .
RUN apk add --no-cache gcc musl-dev sqlite-dev
RUN go mod tidy
RUN CGO_ENABLED=1 GOOS=linux go build -a -ldflags '-extldflags "-static"' -o file-manager main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates sqlite
WORKDIR /root/

COPY --from=builder /app/file-manager .
COPY --from=builder /app/.env .

RUN mkdir -p uploads

EXPOSE 8080

CMD ["./file-manager"]
```

构建和运行：
```bash
docker build -t file-manager-backend .
docker run -d -p 8080:8080 -v $(pwd)/uploads:/root/uploads file-manager-backend
```

### 2. 前端生产部署

#### 构建生产版本
```bash
cd frontend
npm run build
```

#### 使用 Nginx 部署
创建 Nginx 配置文件 `nginx.conf`：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /var/www/file-manager;
    index index.html;
    
    # 前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传大小限制
    client_max_body_size 100M;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 使用 Docker 部署前端
创建前端 `Dockerfile`：
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 3. 使用 Docker Compose 一键部署

创建 `docker-compose.yml`：
```yaml
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    ports:
      - "8080:8080"
    volumes:
      - ./uploads:/root/uploads
      - ./data:/root/data
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=/root/data/file_manager.db
      - JWT_SECRET=your-production-jwt-secret
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - frontend
    restart: unless-stopped

volumes:
  uploads:
  data:
```

启动服务：
```bash
docker-compose up -d
```

## 数据库配置

### SQLite（默认）
无需额外配置，数据库文件会自动创建。

### PostgreSQL
1. 安装 PostgreSQL
2. 创建数据库和用户：
```sql
CREATE DATABASE file_manager;
CREATE USER file_manager_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE file_manager TO file_manager_user;
```

3. 更新环境变量：
```env
DATABASE_URL=postgres://file_manager_user:your_password@localhost/file_manager?sslmode=disable
```

## SSL/HTTPS 配置

### 使用 Let's Encrypt
```bash
# 安装 certbot
sudo apt-get install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### Nginx HTTPS 配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL 配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 性能优化

### 后端优化
1. 启用 Gzip 压缩
2. 配置适当的数据库连接池
3. 使用 Redis 缓存（可选）
4. 配置日志轮转

### 前端优化
1. 启用 Gzip/Brotli 压缩
2. 配置 CDN
3. 启用浏览器缓存
4. 代码分割和懒加载

## 监控和日志

### 日志配置
```bash
# 创建日志目录
mkdir -p /var/log/file-manager

# 配置 logrotate
sudo vim /etc/logrotate.d/file-manager
```

logrotate 配置：
```
/var/log/file-manager/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

### 系统服务配置
创建 systemd 服务文件 `/etc/systemd/system/file-manager.service`：
```ini
[Unit]
Description=File Manager Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/file-manager
ExecStart=/opt/file-manager/file-manager
Restart=always
RestartSec=5
Environment=ENVIRONMENT=production

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl enable file-manager
sudo systemctl start file-manager
```

## 备份策略

### 数据库备份
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/file-manager"

mkdir -p $BACKUP_DIR

# SQLite 备份
cp /path/to/file_manager.db $BACKUP_DIR/file_manager_$DATE.db

# 文件备份
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /path/to/uploads/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

设置定时备份：
```bash
# 添加到 crontab
0 2 * * * /path/to/backup.sh
```

## 故障排除

### 常见问题

1. **端口被占用**
```bash
sudo lsof -i :8080
sudo kill -9 <PID>
```

2. **权限问题**
```bash
sudo chown -R www-data:www-data /path/to/uploads
sudo chmod -R 755 /path/to/uploads
```

3. **数据库连接失败**
- 检查数据库服务状态
- 验证连接字符串
- 检查防火墙设置

4. **文件上传失败**
- 检查上传目录权限
- 验证文件大小限制
- 检查磁盘空间

### 日志查看
```bash
# 后端日志
tail -f /var/log/file-manager/app.log

# Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 系统服务日志
journalctl -u file-manager -f
```

## 安全建议

1. **定期更新依赖**
2. **使用强密码和密钥**
3. **配置防火墙**
4. **启用访问日志**
5. **定期备份数据**
6. **监控异常访问**
7. **使用 HTTPS**
8. **限制文件上传类型和大小**

## 扩展部署

### 负载均衡
使用 Nginx 或 HAProxy 进行负载均衡：
```nginx
upstream backend {
    server backend1:8080;
    server backend2:8080;
    server backend3:8080;
}

server {
    location /api/ {
        proxy_pass http://backend;
    }
}
```

### 集群部署
1. 使用共享存储（NFS/S3）
2. 配置数据库集群
3. 使用 Redis 共享会话
4. 配置服务发现

这样就完成了完整的部署指南，涵盖了从开发环境到生产环境的各种部署场景。