package main

import (
	"crypto/sha256"
	"fmt"
	"io"
	"log"
	"os"

	"file-manager/config"
	"file-manager/database"
	"file-manager/models"
)

func calculateFileHash(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hasher := sha256.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hasher.Sum(nil)), nil
}

func main() {
	// 加载配置
	cfg := config.Load()

	// 连接数据库
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 获取所有hash为空的文件
	var files []models.File
	if err := db.Where("hash = '' OR hash IS NULL").Find(&files).Error; err != nil {
		log.Fatal("Failed to fetch files:", err)
	}

	log.Printf("Found %d files without hash, updating...", len(files))

	updated := 0
	failed := 0

	for _, file := range files {
		// 检查文件是否存在
		if _, err := os.Stat(file.Path); os.IsNotExist(err) {
			log.Printf("File not found: %s, skipping", file.Path)
			failed++
			continue
		}

		// 计算hash
		hash, err := calculateFileHash(file.Path)
		if err != nil {
			log.Printf("Failed to calculate hash for %s: %v", file.Path, err)
			failed++
			continue
		}

		// 更新数据库
		if err := db.Model(&file).Update("hash", hash).Error; err != nil {
			log.Printf("Failed to update hash for file ID %d: %v", file.ID, err)
			failed++
			continue
		}

		updated++
		log.Printf("Updated hash for file ID %d: %s", file.ID, file.OriginalName)
	}

	log.Printf("Hash update completed. Updated: %d, Failed: %d", updated, failed)
}
